import json
import requests
from bson import ObjectId
import datetime
# from .track_time import time_it
from src.v2.dashboard.cta.routes import create_cta, CTACreate
import asyncio
from typing import Optional, Union

from src.v2.dashboard.cta.models import CTAType
# Dummy function definitions

# @time_it
# def get_current_date():
#     """
#     Dummy function to simulate getting the current date.
#     """
#     return {"current_date": datetime.date.today().strftime("%Y-%m-%d")}





# @time_it
def create_issue_tickets(name:str, issue_type:str, description:str, user_req, current_user,channel:Optional[str] = None, tenant_name:Optional[str] = None):
    """
    Dummy function to simulate creating an issue ticket.
    use the previous_message to understand the context of the issue,
    if any images are present in the previous message provide that as resources for the issue
    """
    print("create_issue_tickets issue_type", issue_type)
    customer_info=current_user.db.customers.find_one({"customer_id": user_req.customer_id},{"_id":0,"customer_name":1,"whatsapp_id":1,"whatsapp_number":1,"email":1,"phone_number":1,"country_code":1})

    # Validate and handle null/invalid issue_type

    # Default to "ticket" if issue_type is null, empty, or invalid
    valid_cta_type = "ticket"  # Default value

    if issue_type:
        # Clean and normalize the issue_type
        normalized_type = str(issue_type).lower().strip()

        # Check if it's a valid CTAType
        try:
            if normalized_type == "ticket":
                valid_cta_type = CTAType.TICKET.value
            elif normalized_type == "booking":
                valid_cta_type = CTAType.BOOKING.value
            else:
                print(f"Warning: Invalid issue_type '{issue_type}', defaulting to 'ticket'")
                valid_cta_type = CTAType.TICKET.value
        except Exception as e:
            print(f"Error validating issue_type '{issue_type}': {e}, defaulting to 'ticket'")
            valid_cta_type = CTAType.TICKET.value
    else:
        print(f"Warning: issue_type is null/empty, defaulting to 'ticket'")
        valid_cta_type = CTAType.TICKET.value
    
    tenant_name = current_user.slug

    # Get tenant name from current_user if not provided (for backward compatibility)
    if tenant_name is None:
        tenant_name = getattr(current_user, 'tenant_id', 'default')
    print("tenant_name",tenant_name)

    # Customize ticket title based on tenant
    if valid_cta_type == "ticket":
        if tenant_name == "dramit" or tenant_name == "dramit-dev":
            ticket_title = "Medical Issue Ticket"
        elif tenant_name == "techspire":
            ticket_title = "Academic Issue Ticket"
        else:
            ticket_title = f"{valid_cta_type} Acknowledgement"
    elif valid_cta_type == "booking":
        if tenant_name == "dramit" or tenant_name == "dramit-dev":
            ticket_title = "Medical Appointment Booking"
        elif tenant_name == "techspire":
            ticket_title = "Academic Consultation Booking"
        else:
            ticket_title = f"{valid_cta_type} Acknowledgement"
    else:
        ticket_title = f"{valid_cta_type} Acknowledgement"

    print(f"Creating issue ticket with type: {valid_cta_type}, channel: {channel}, tenant: {tenant_name}")

    resp_ = asyncio.run(create_cta(
        CTACreate(
            name = ticket_title,
            customer_name = name,
            type = valid_cta_type,
            description = description,
            channel = channel,
            customer_id = user_req.customer_id,
            extra_info = customer_info
        ), current_user
    ))

    cta_id = resp_.data.id

    # Create a clean response dictionary without serialization issues
    resp_d = {
        "success": resp_.success,
        "cta_id": str(cta_id),
        "message": resp_.message if resp_.message else "CTA created successfully",
        "error": resp_.error if resp_.error else None
    }

    return resp_d


# @time_it
def handle_booking(customer_name, customer_phone_no, customer_age=None, 
                  customer_medical_history=None, description=None, user_req=None, current_user=None):
    print("handle_booking", user_req.channel)

    # For backward compatibility, use provided values or defaults
    name = customer_name
    phone_no = customer_phone_no
    age = customer_age
    medical_history = customer_medical_history

    # Get tenant name from current_user if not provided
    tenant_name = current_user.slug
    if tenant_name is None:
        tenant_name = getattr(current_user, 'tenant_id', 'default')

    # Customize response based on tenant
    if tenant_name == "dramit" or tenant_name == "dramit-dev":
        # Medical context
        booking_title = "Medical Appointment Booking"
        field_label = "Medical History"
        booking_type = "medical appointment"
        acknowledgment_msg = f"Medical appointment booking for patient {name} with phone number {phone_no} has been acknowledged."

    elif tenant_name == "techspire":
        # Educational context
        booking_title = "Academic Consultation Booking"
        field_label = "Academic Background"
        booking_type = "academic consultation"
        acknowledgment_msg = f"Academic consultation booking for student {name} with phone number {phone_no} has been acknowledged."

    else:
        # Default/general context
        booking_title = "Booking Acknowledgement"
        field_label = "Additional Information"
        booking_type = "consultation"
        acknowledgment_msg = f"Booking for {name} with phone number {phone_no} has been acknowledged."

    # Build description with conditional parts
    booking_description = f"{description}"
    
    # Add age if provided
    if age:
        booking_description += f"\nAge: {age}"
    
    # Add field-specific information if provided
    if medical_history:
        booking_description += f"\n{field_label}: {medical_history}"
    
    # Always add acknowledgment message
    booking_description += f"\n{acknowledgment_msg}"

    data = create_issue_tickets(
        name = booking_title,
        issue_type = "booking",
        description = booking_description,
        user_req = user_req,
        current_user = current_user,
        channel=user_req.channel
    )

    # Create a clean response message
    response = {
        "message": acknowledgment_msg,
        "status": "success",
        "booking_type": booking_type,
        "tenant": tenant_name
    }

    return response, data




# @time_it
def initial_address_information(db):
    """Initial Address Information for new conversations"""
    INITIAL_ADDRESS_PROMPT = db.prompt.find_one({"name": "initial_address_information"}, {"_id": 0, "name": 0})

    org_detail = db.business_info.find_one({"name": "BusinessInfo"}, {"_id": 0}) or {}

    # Fallbacks for missing keys
    greeting_text = INITIAL_ADDRESS_PROMPT.get("text", "").format(
        org_name=org_detail.get("org_name", ""),
        agent_name=org_detail.get("agent_name", "AI Bot"),
        org_description=org_detail.get("org_description", "")
    )

    return {"initial_greeting": greeting_text}
