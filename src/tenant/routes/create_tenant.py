
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
import json
from dotenv import load_dotenv
import asyncio
from pymongo import AsyncMongoClient

from src.core.database import get_admin_db
from src.core.security import get_tenant_info
from src.models.user import UserTenantDB
from src.tenant.module.create_tenent import Tenant

from qdrant_client.http.models import Filter, FieldCondition, MatchValue

from typing import Optional
from pydantic import BaseModel
from src.tenant.module.tenant_setup import BusinessInfo
from src.models.user import UserTenantDB
from src.v2.KB.qdrant.qdrant_client import Qdrant_Call, QdrantConfig
from src.v2.KB.kb_setup.handle_documents import Data, add_documents_json
from src.v3.chat.dynamic_reply import generate_response_openai
from src.streamlitdemo.chatdemo.respond import MsgRequest
from src.helper.qdrant import (
    fetch_qdrant_config,
    initialize_clients
)
from src.helper.resolve_llm import resolve_llm

tenant_router = APIRouter(
    prefix="/tenants",
    tags=["Tenants"],
)

class TenantCreate(BaseModel):
    tenant_name: str
    tenant_slug: str


@tenant_router.post("/create-tenents")
async def create_tenant(
    request: TenantCreate=Depends(),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    print("current_user",current_user.user.role,current_user.user.username)
    if current_user.user.role != "admin" or current_user.user.username != "superadmin":
        raise HTTPException(status_code=403, detail="You are not authorized for this action")
    if not request.tenant_name or not request.tenant_slug:
        raise HTTPException(status_code=400, detail="Tenant name and slug are required")
    
    requirements=get_admin_db().requirements.find_one({"name":"new_tenant_requirement"})
    
    tenant = Tenant(request.tenant_name, request.tenant_slug,requirements)
    try:
        await tenant._register_tenant()
        await tenant._preapre_client_database()
        await tenant._insert_default_data()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
    return {"message": "Tenant created successfully make sure to change tenant_id in data"}




# class Stage1Request(BaseModel):
#     business_info: BusinessInfo  # agent_role = goal

async def get_questions_by_org_type(doc, org_type):
    questions = []
    # Convert org_type to lowercase for case-insensitive comparison
    org_type_lower = org_type.lower() if isinstance(org_type, str) else ""
    
    for entry in doc.get("data", []):
        if entry["type"].lower() == org_type_lower:
            questions.extend(entry.get("questions", []))
    return list(set(questions)) 



@tenant_router.post("/promtp-test")
async def agent_goal_prompt(user_additional_goal: str, choosed_goal_type: str, current_user: UserTenantDB = Depends(get_tenant_info)):
    agent_goal_doc = get_admin_db().requirements.find_one({"name": "org_type"}, {"_id": 0})
    
    main_prompt = None
    for org_type in agent_goal_doc.get("types", []):
        for goal in org_type.get("agent_goals", []):
            if goal.get("prompt_type") == choosed_goal_type:
                main_prompt = goal.get("prompt")
                break
        if main_prompt:
            break

    if not main_prompt:
        return {"error": f"Prompt type '{choosed_goal_type}' not found."}

    # Append user additional goal if given
    if user_additional_goal and len(user_additional_goal.strip()) > 1:
        PROMPT_NAME = "agent_goal_refine_prompt"
        prompt = current_user.db["prompt"].find_one({"name": PROMPT_NAME})
        print("prompttt",prompt)

        try:
            llm = resolve_llm(model_name=prompt.get("model"))
            trailored_prompt= llm.complete(
                prompt.get("text").format(
                    user_additional_goal=user_additional_goal,
                    main_prompt=main_prompt,
                ),
                formatted=True,
            ).text
        except Exception as e:
            trailored_prompt=main_prompt
    else:
        trailored_prompt=main_prompt

    return trailored_prompt




@tenant_router.post("/stage-1")
async def stage_1(
    data: BusinessInfo,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    # business_data = data.business_info.model_dump()
    # business_data = {k: v for k, v in data.business_info.model_dump().items() if v is not None}
    choosed_goal_type=data.agent_goal_type
    user_additional_goal=data.additional_agent_goal 
    new_agent_prompt= await agent_goal_prompt(user_additional_goal,choosed_goal_type,current_user)

    business_data = {k: v for k, v in data.model_dump().items() if v is not None}
    business_data['agent_goal'] = new_agent_prompt



    current_user.db.business_info.update_one(
        {"name": "BusinessInfo"},
        {"$set": business_data},
        upsert=True
    )

    qd_client = Qdrant_Call(config=QdrantConfig(**current_user.qdrant_config))
    org_type = data.org_type

    questions_doc = get_admin_db().requirements.find_one({"name": "setup_questions"})
   
    # if org_type == "Sales":
    #     questions = questions_doc.get("questions", {}).get("sales_question", [])
    # else:
    #     questions = []
    questions = await get_questions_by_org_type(questions_doc, org_type)

    print("questions", questions)

    # simplified_prompt, standard_prompt, elobrated_prompt = data.format_dummy_prompt(current_user)
    data.format_dummy_prompt(current_user)
    
    
    prompt_variants = [
        ("simplified"),
        ("standard"),
        ("elaborated")
    ]

    async def generate_single_response(question: str, style: str) -> dict:
        """Helper function to generate a single response for a question and style"""
        if style == "simplified":
            system_prompt = current_user.db.prompt.find_one(
                {"name": "reply_prompt_openai_simplified"}
            )
        elif style == "elaborated":
            system_prompt = current_user.db.prompt.find_one(
                {"name": "reply_prompt_openai_elaborated"}
            )
        else:
            system_prompt = current_user.db.prompt.find_one({"name": "reply_prompt_openai"})

        if not system_prompt:
            raise HTTPException(status_code=500, detail="System prompt not found")

        try:
            reply, _ = await generate_response_openai(
                MsgRequest(
                    customer_id=current_user.user.id,
                    message=question,
                    channel="Playground",
                ),
                system_prompt,
                qd_client,
                current_user,
            )
            return {
                "style": style,
                "user_query": question,
                "response_example": reply if isinstance(reply, str) else str(reply)
            }
        except Exception as e:
            return {
                "style": style,
                "user_query": question,
                "error": f"Error processing response: {str(e)}"
            }

    # Create all tasks
    tasks = []
    for question in questions:
        for mode in ["simplified", "standard", "elaborated"]:
            tasks.append(generate_single_response(question, style=mode))
    # Run all tasks concurrently
    example_responses = await asyncio.gather(*tasks)

    return {
        "questions": questions,
        "prompt_variants": example_responses
    }





@tenant_router.get("/tools")
async def setup_tenant(current_user: UserTenantDB = Depends(get_tenant_info)):
    tool_config = get_admin_db().requirements.find_one({"name": "tool_setup"})
    available_tools = tool_config.get("available_tools", []) if tool_config else []

    return available_tools
    



class Stage2Request(BaseModel):
    prompt: str
    tools: list[str]
    ticket_required_info:Optional[list[str]]=None
    AI_greeting:Optional[bool]=False



@tenant_router.post("/stage-2")
async def setup_tenant_tools(
    data: Stage2Request, 
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    try:
        tenant_doc = current_user.db.business_info.find_one({"name":"BusinessInfo"})
        if not tenant_doc:
            raise HTTPException(status_code=400, detail="Business info not found. Please complete stage-1 first.")

        # business_info = BusinessInfo(**tenant_doc["business_info"])

        # tools_list = []
        # tool_setup = get_admin_db().requirements.find_one({"name": "tool_setup"})
        # if not tool_setup:
        #     raise HTTPException(status_code=404, detail="Tool setup configuration not found")

        # tool_mapping = {tool["name"]: tool["description"] 
        #                for tool in tool_setup.get("available_tools", [])}

        

        result = BusinessInfo.update_prompt(
            prompt=data.prompt,  
            tools=data.tools,
            ticket_required_info=data.ticket_required_info,
            AI_greeting=data.AI_greeting,

            # business_info=business_info,
            current_user=current_user
        )
        
        return {
            "result": result
        }
    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    


@tenant_router.post("/dummy-data")
async def dummy_data(org_type: str, current_user: UserTenantDB = Depends(get_tenant_info)):
    try:
        # Fetch the dummy data document
        dummy_data_doc = get_admin_db().requirements.find_one({"name": "setup_questions"})
        if not dummy_data_doc or "data" not in dummy_data_doc:
            raise HTTPException(status_code=500, detail="Dummy data setup is missing.")

        # Search for matching entry (since data is a list, not a dict)
        entry = next((item for item in dummy_data_doc["data"] if item["type"] == org_type), None)
        if not entry:
            raise HTTPException(status_code=404, detail="No dummy data found for this type.")

        dummy_text = entry["dummy_data"]
        metadata = entry["dummy_metadata"]

        # Fetch Qdrant and MinIO clients
        qdrant_config, minio_config = await fetch_qdrant_config(current_user)
        qarant, minio = await initialize_clients(qdrant_config, minio_config)

        # Check if metadata already exists in Qdrant
        filter_condition = Filter(
            must=[
                FieldCondition(key="category", match=MatchValue(value=metadata["category"])),
                FieldCondition(key="type", match=MatchValue(value=metadata["type"])),
            ]
        )
        try:
            count_result = qarant.client_().count(
                collection_name=qdrant_config["page_collection"],
                exact=True,
                count_filter=filter_condition
            )
            if count_result.count > 0:
                return {"message": "Documents with this metadata already exist"}

        except Exception as e:
            # Likely the collection doesn't exist yet
            import traceback
            traceback.print_exc()
            # Log or ignore the error — we’ll proceed with insertion
            pass
        # Insert dummy data
        data_json = {
            "text": dummy_text,
            "metadata": metadata
        }

        formatted_data = [Data(**data_json)]
        await add_documents_json(formatted_data=formatted_data, current_user=current_user)

        return {"status": "dummy data uploaded"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@tenant_router.get("/org-type")
async def get_org_types(current_user: UserTenantDB = Depends(get_tenant_info)):
    org_type_doc = get_admin_db().requirements.find_one({"name": "org_type"})
    types = org_type_doc.get("types", [])

    simplified_types = []
    for type_data in types:
        simplified_type = {
            "name": type_data.get("name"),
            "label": type_data.get("label"),
            "agent_goals": [
                {
                    "prompt_type": goal.get("prompt_type", ""),
                    "prompt_summary": goal.get("prompt_summary", "")
                }
                for goal in type_data.get("agent_goals", [])
            ]
        }
        simplified_types.append(simplified_type)

    return simplified_types

@tenant_router.post("/language-list")
async def get_language(current_user:UserTenantDB=Depends(get_tenant_info)):
    language=get_admin_db().requirements.find_one({"name":"language_setup"})
    language_list=language.get("language",[])
    return language_list




@tenant_router.get("/business-info")
async def get_business_info(current_user: UserTenantDB = Depends(get_tenant_info)):
    tenant_doc = current_user.db.business_info.find_one(
        {"name": "BusinessInfo"},
        {"_id": 0,"name":0,"set_up_complete":0}  
    )

    if not tenant_doc:
        return {
            "status": "error",
            "message": "Business info not found"
        }
    
    return {
        "status": "success",
        "result": tenant_doc
    }

class BusinessInfoEdit(BaseModel):
    org_name: Optional[str] = None
    org_type: Optional[str] = None
    org_description: Optional[str] = None
    org_contact: Optional[str] = None
    org_email: Optional[str] = None
    org_goal: Optional[str] = None
    agent_name: Optional[str] = None
    agent_role: Optional[str] = None
    agent_goal: Optional[str] = None
    language:Optional[list[str]]=None
    additional_agent_goal:Optional[str]=None
    agent_goal_type:Optional[str]=None
    preferred_prompt:Optional[str] = None
    used_tools:Optional[list[str]]=None
    ticket_required_info: Optional[list[str]]=[]
    AI_greeting:bool=False


@tenant_router.post("/edit-info")
async def edit_business_info(
    data: BusinessInfoEdit,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    update_data = data.model_dump(exclude_unset=True)
    
    if not update_data:
        return {"status": "error", "message": "No valid fields provided for update"}

    # Check if both required fields are present to call agent_goal_prompt
    additional_agent_goal = update_data.get("additional_agent_goal")
    agent_goal_type = update_data.get("agent_goal_type")

    tool_update=update_data.get("used_tools")

    if tool_update:
        nav_access_doc = current_user.db.settings.find_one({"name": "nav_access"})
        if not nav_access_doc:
            return {"status": "error", "message": "nav_access settings not found"}

        updates = {}
        for role in ["admin", "agent"]:
            for tool in tool_update:
                updates[f"{role}.CTA.type.{tool}"] = True
                updates[f"{role}.ai_response.CTA_type.{tool}"] = True

        current_user.db.settings.update_one(
            {"name": "nav_access"},
            {"$set": updates}
        )

        # in tool update enum
        tool_doc = current_user.db.tools.find_one({"name": "create_issue_tickets"})
        if not tool_doc:
            return {"status": "error", "message": "create_issue_tickets tool not found"}

        enum_path = tool_doc["tools"][0]["function"]["parameters"]["properties"]["_issue_type"]["enum"]
        updated_enum = list(set(enum_path + tool_update))  # Merge and remove duplicates

        current_user.db.tools.update_one(
            {"name": "create_issue_tickets"},
            {
                "$set": {
                    "tools.0.function.parameters.properties._issue_type.enum": updated_enum
                }
            }
        )
    # ticket_required_info = update_data.get("ticket_required_info")
    # if ticket_required_info:
    #     tool_doc = current_user.db.tools.find_one({"name": "create_issue_tickets"})
    #     if not tool_doc:
    #         return {"status": "error", "message": "create_issue_tickets tool not found"}

    #     # Default required fields
    #     default_required = ["_description", "_issue_type"]
    #     # Merge with `ticket_required_info`, ensuring no duplicates
    #     updated_required = list(set(default_required + ticket_required_info))

    #     current_user.db.tools.update_one(
    #         {"name": "create_issue_tickets"},
    #         {
    #             "$set": {
    #                 "tools.0.function.parameters.required": updated_required
    #             }
    #         }
    #     )


    if additional_agent_goal and agent_goal_type:
        new_agent_prompt = await agent_goal_prompt(
            additional_agent_goal,
            agent_goal_type,
            current_user
        )
        # If your agent_goal_prompt returns a dict like {"agent_goal_prompt": prompt}, extract string:
        if isinstance(new_agent_prompt, dict):
            new_agent_prompt = new_agent_prompt.get("agent_goal_prompt", None)

        if new_agent_prompt:
            update_data["agent_goal"] = new_agent_prompt

    # Always set set_up_complete to True on update
    update_data["set_up_complete"] = True

    current_user.db.business_info.update_one(
        {"name": "BusinessInfo"},
        {"$set": update_data},
        upsert=True
    )

    return {"status": "success", "updated_fields": list(update_data.keys())}


@tenant_router.get("/setup-check")
async def setup_check(current_user: UserTenantDB = Depends(get_tenant_info)):
    check = current_user.db.business_info.find_one({"name": "BusinessInfo"})

    if not check:
        raise HTTPException(status_code=400, detail="Business Info not found")

    verified = check.get("set_up_complete", False)

    if not verified:
        raise HTTPException(status_code=400, detail="Setup not complete")

    return {
        "status": "success"
    }
