from src.routes.reply import get_tenant_info
from src.v3.chat.routes import generate_reply

from fastapi import APIRouter, Request, BackgroundTasks
import requests
from datetime import datetime
from src.v2.models.chat_hist import CurrentUserContext, ModelRunRequest, ChatHistMessage
from src.core.database import get_async_db_from_tenant_id
from src.helper.logger import setup_new_logging
from src.v2.widget.chat.utils.db_operations import save_user_message, save_assistant_reply
from src.v2.external_hooks.social_media_webhooks.client import get_sociar_client
from src.routes.reply import  get_tenant_info
from src.v3.chat.routes import generate_reply

logger = setup_new_logging(__name__)

router = APIRouter(tags=["Social Media WebHooks"], prefix="/sociar_webhook")


@router.post("/{tenant_id}/", include_in_schema=True)
async def social_media_webhook(tenant_id: str, request: ModelRunRequest, background_tasks: BackgroundTasks):
    try:
        body = request.model_dump()
        logger.info(f"Incoming social media webhook for tenant: {tenant_id}")
        logger.debug(f"Webhook body: {body}")
        
        db = get_async_db_from_tenant_id(tenant_id)
        tenant_name= db.name
        sociar_env = await db.settings.find_one({"name": "sociar_env"})

        if not sociar_env:
            logger.error("Sociar environment settings not found")
            return {"error": "Sociar environment settings not found"}, 500

        WHATSAPP_EKO_TOKEN = sociar_env.get("WHATSAPP_EKO_BOT_ACESS_TOKEN")
        if not WHATSAPP_EKO_TOKEN:
            logger.error("WhatsApp EKO token not configured")
            return {"error": "WhatsApp EKO token not configured"}, 500
            
        # Import these locally to avoid circular imports

        current_user = await get_tenant_info(WHATSAPP_EKO_TOKEN)
        client = await get_sociar_client(current_user)

        client_id=request.user_id
        exising_customer = await db.customers.find_one({"customer_id": client_id})
        if not exising_customer:
            logger.info(f"New customer detected: {client_id}")
            # Insert new customer and get the inserted document
            insert_result = await db.customers.insert_one(
                    {
                        "customer_name": request.user_name,
                        "customer_id": request.user_id or request.phone_number or None,
                        "profile_id": request.profile_id or None,
                        "whatsapp_number": request.from_number or None ,

                        "country_code": request.country_code or None,
                        "phone_number": request.phone_number or None,
                        "email": request.email or None,
                        "channel": request.channel.capitalize() or request.profile.type.capitalize(),
                        "created_at": datetime.now(),
                        "last_active_at": datetime.now(),
                    }
                )
            logger.info(f"New user created with ID: {insert_result}")

        # Channel detection and validation
        channel =  body.get("channel", "").lower()
        if channel not in ("whatsapp", "facebook", "instagram"):
            logger.error(f"Unsupported channel: {channel}")
            return {"error": "Unsupported channel"}, 400

        conversation_id = body.get("conversation_id")
        if not conversation_id:
            logger.error("conversation_id missing in webhook body")
            return {"error": "conversation_id missing"}, 400

        # Message content validation - extract latest message using role_data format logic
        # Following the same pattern as reply_generate function

        chat_data, latest_message ,image_process_metadata= await request.get_formatted_messages(current_user=current_user)

        # Save the incoming user message to database
        try:
            CurrentUserContext.set_current_user(current_user)
            user_message = ChatHistMessage(
                role="user",
                content=latest_message.content,
                sender="user",
                created_at=datetime.now(),
                user_id=str(body.get("user_id", "user_id_placeholder")),
                # media_ids=latest_message.media_ids,
                media_ids = getattr(latest_message, "media_ids", []) or [],

                media_values=latest_message.media_values,
                verified=True
                # Don't set id field - let the model validator generate it
            )
            user_message_id = await save_user_message(user_message, current_user)
            logger.info(f"Saved user message with ID: {user_message_id}")
        except Exception as e:
            logger.error(f"Failed to save user message: {str(e)}")
            # Continue processing even if saving fails

        # Generate reply using the AI system
        chat_data = {
            "role": "user",
            "data": {"content": latest_message.content,
                      "media_ids": latest_message.media_ids if latest_message.media_ids else [],
                      "created_at": datetime.now()},
        }
        model_request = ModelRunRequest(
            chat_data=[chat_data],
            chat_data_format="role_data",
            media_ids=[],
            media_values="",
            user_id=str(body.get("user_id", "user_id_placeholder")),
            user_name=body.get("user_name", "user_name_placeholder"),
            channel=request.channel.capitalize(),
            mode="elaborated",
            conversation_id=body.get("conversation_id"),
            profile=body.get("profile")
        )

        response, response_id = await generate_reply(model_request, background_tasks, current_user)
        print("rreessppoonnssee",response)
        CTA_info = response.get("response", {}).get("information_gathering", [])
        cta_details = [
            {
                "function_name": cta.get("function_name"),
                "function_args": cta.get("function_args")
            }
            for cta in CTA_info if cta.get("function_name") and cta.get("function_args")
        ]
        reply_text = response["response"]["reply"]
        logger.info(f"Generated reply: {reply_text}")

        # Extract reply URLs from the response
        reply_urls = response["response"].get("reply_urls", [])
        logger.info(f"Reply URLs from response: {reply_urls}")

        # Extract URLs from source nodes if they exist
        if "source_nodes" in response["response"] and response["response"]["source_nodes"]:
            extracted_urls = []
            for node in response["response"]["source_nodes"]:
                # Check if the node has resource_url
                if "metadata" in node and "resource_url" in node["metadata"]:
                    resource_url = node["metadata"]["resource_url"]
                    if isinstance(resource_url, list):
                        for url in resource_url:
                            if isinstance(url, dict) and "url" in url:
                                extracted_urls.append(url["url"])
                            elif isinstance(url, str):
                                extracted_urls.append(url)
                    elif isinstance(resource_url, str):
                        extracted_urls.append(resource_url)

            # If we found URLs, add them to reply_urls
            if extracted_urls:
                reply_urls.extend(extracted_urls)
                logger.info(f"Extracted {len(extracted_urls)} URLs from source nodes")

        # Remove duplicates
        reply_urls = list(set(reply_urls))
        logger.info(f"Final reply URLs: {reply_urls}")

        # Save the assistant reply to database
        try:
            assistant_reply = ChatHistMessage(
                role="assistant",
                content=reply_text,
                sender="assistant",
                created_at=datetime.now(),
                user_id=str(body.get("user_id", "user_id_placeholder")),
                media_ids=reply_urls,  # Include the reply URLs as media IDs
                media_values="",
                verified=True
                # Don't set id field - let the model validator generate it
            )
            assistant_reply_id = await save_assistant_reply(assistant_reply, set(reply_urls), current_user)
            logger.info(f"Saved assistant reply with ID: {assistant_reply_id} and {len(reply_urls)} media URLs")
        except Exception as e:
            logger.error(f"Failed to save assistant reply: {str(e)}")
            # Continue processing even if saving fails

        # Send response via appropriate channel
        result = None
        # if channel == "whatsapp":
        #     # result = await _handle_whatsapp_response(
        #     #     client, body, conversation_id, reply_text, reply_urls, sociar_env
        #     # )
        #     result = await _handle_whatsapp_response(
        #         client, conversation_id, reply_text, reply_urls
        #     )
        # elif channel == "facebook":
        #     result = await _handle_facebook_response(
        #         client, conversation_id, reply_text, reply_urls
        #     )
        # elif channel == "instagram":
        #     result = await _handle_instagram_response(
        #         client, conversation_id, reply_text, reply_urls
        #     )

        # if not result or result.get("status") != "success":
        #     logger.error(f"Failed to send message: {result}")
        #     return {"error": f"Failed to send reply message via {channel}"}, 500

        return {
            "status": "success",
            "channel": request.channel,
            "reply": reply_text,
            "reply_urls": reply_urls,
            "CTA":cta_details,
            "profile_id": request.profile_id,
            "user_id": body.get("user_id"),
            "response_id": response_id
        }

    except requests.RequestException as e:
        logger.error(f"API request failed: {str(e)}")
        return {"error": "Message service unavailable"}, 503
    except Exception as e:
        logger.error(f"Unexpected error in webhook: {str(e)}", exc_info=True)
        return {"error": "Internal server error"}, 500


async def _handle_whatsapp_response(client, conversation_id, reply_text, reply_urls):
    """Handle WhatsApp response sending."""

    # Merge reply URLs into the reply text
    final_message = reply_text
    if reply_urls:
        logger.info(f"Merging {len(reply_urls)} URLs into WhatsApp message")
        final_message += "\n\n" + "\n".join(reply_urls)

    # Send message with merged content
    result = client.send_whatsapp_message(int(conversation_id), final_message)

    return result


async def _handle_facebook_response(client, conversation_id, reply_text, reply_urls):
    """Handle Facebook response sending."""
    # Merge reply URLs into the reply text
    final_message = reply_text.strip() if reply_text else ""
    
    if reply_urls:
        reply_urls = [url for url in reply_urls if url]
        if reply_urls:
            logger.info(f"Merging {len(reply_urls)} URLs into Facebook message")
            final_message += "\n\n" + "\n".join(reply_urls)

    # Trim again to avoid sending only newlines
    final_message = final_message.strip()

    if not final_message:
        logger.error("Cannot send empty message: both text and attachments are missing.")
        raise ValueError("Facebook message must have either content or attachments.")

    # Send message with merged content
    result = client.send_facebook_message(int(conversation_id), final_message)
    return result



async def _handle_instagram_response(client, conversation_id, reply_text, reply_urls):
    """Handle Instagram response sending."""
    # Merge reply URLs into the reply text
    final_message = reply_text
    if reply_urls:
        logger.info(f"Merging {len(reply_urls)} URLs into Instagram message")
        final_message += "\n\n" + "\n".join(reply_urls)

    # Send message with merged content
    result = client.send_instagram_message(int(conversation_id), final_message)

    return result


# Legacy webhook endpoint removed - using only modern unified webhook