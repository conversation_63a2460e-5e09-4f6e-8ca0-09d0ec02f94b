from fastapi import APIRouter, Depends, HTTPException
from src.core.security import get_tenant_info
from src.models.user import UserTenantDB
from src.models.chat_hist import InformationGathering
from src.v2.KB.qdrant.qdrant_client import Qdrant_Call, QdrantConfig
from src.reply.minio_client import MinIO<PERSON>lient, MinIOConfig
from qdrant_client.http import models
import asyncio
import json
from bson import ObjectId
from src.v2.dashboard.channels.models import Message
from typing import List, Optional
from src.helper.logger import setup_new_logging
from fastapi.responses import JSONResponse
# Removed unused imports: ThreadPoolExecutor, as_completed
from bson import ObjectId
from urllib.parse import urlparse, urlunparse
from src.v2.dashboard.cta.service import get_cta_item
loggers = setup_new_logging(__name__)


router=APIRouter(tags=["Channels"])

def strip_query(url):
    parsed = urlparse(url)
    return urlunparse(parsed._replace(query=""))



@router.post("/fetch_conversation")
async def fetch_conversation(
    user_id: str,
    limit: Optional[int] = 5,
    page_number: int = 1,
    current_user: UserTenantDB = Depends(get_tenant_info),
):
    loggers.info(f"Fetching conversation for user ID: {user_id}")
    skip = (page_number - 1) * limit

    # Use async database operations
    db = current_user.async_db

    # Run total count and conversations fetch in parallel
    total_count_task = asyncio.create_task(
        get_total_count_async(db, user_id)
    )

    # Setup MinIO client
    env_var = await db.settings.find_one({"name": "env"})
    if not env_var:
        raise HTTPException(status_code=500, detail="Environment configuration not found")

    minio_config = env_var.get("minio_config", {})
    minio_client = MinIOClient(
        config=MinIOConfig(
            access_key=minio_config.get("access_key"),
            secret_key=minio_config.get("secret_key"),
            minio_url=minio_config.get("minio_url"),
            bucket_name=minio_config.get("bucket_name"),
        )
    )

    # Use optimized aggregation pipeline to fetch conversations with related data
    conversation_pipeline = build_conversation_pipeline(user_id, skip, limit)
    conversations_task = asyncio.create_task(
        ( await db.ai_response.aggregate(conversation_pipeline)).to_list(limit)
    )

    # Wait for both tasks to complete
    total_items, conversations = await asyncio.gather(total_count_task, conversations_task)

    # Process conversations in parallel using asyncio.gather for better performance
    if conversations:
        tasks = [
            process_conversation_async(conversation, minio_client)
            for conversation in conversations
        ]
        chat_history = await asyncio.gather(*tasks, return_exceptions=True)

        # Filter out exceptions and empty results
        chat_history = [
            chat for chat in chat_history
            if not isinstance(chat, Exception) and chat
        ]
    else:
        chat_history = []

    return {
        "data": chat_history,
        "meta": {
            "page_number": page_number,
            "limit": limit,
            "total_items": total_items,
        }
    }


async def get_total_count_async(db, user_id: str) -> int:
    """Get total count of conversations for a user asynchronously"""
    total_count_pipeline = [
        {"$match": {"request.user_id": user_id}},
        {"$count": "total"}
    ]
    total_result = await (await db.ai_response.aggregate(total_count_pipeline)).to_list(1)
    return total_result[0]["total"] if total_result else 0


def build_conversation_pipeline(user_id: str, skip: int, limit: int):
    """Build optimized aggregation pipeline for fetching conversations with related data"""
    return [
        # Match conversations for the user
        {
            "$match": {
                "request.user_id": user_id
            }
        },
        # Sort by creation date (newest first)
        {
            "$sort": {
                "created_at": -1
            }
        },
        # Apply pagination
        {
            "$skip": skip
        },
        {
            "$limit": limit
        },
        # Lookup chat messages in a single operation
        {
            "$lookup": {
                "from": "chat_messages",
                "let": {"chat_ids": "$response.chat_ids"},
                "pipeline": [
                    {
                        "$match": {
                            "$expr": {
                                "$in": ["$_id", {
                                    "$map": {
                                        "input": "$$chat_ids",
                                        "as": "id",
                                        "in": {"$toObjectId": "$$id"}
                                    }
                                }]
                            }
                        }
                    },
                    {
                        "$sort": {"created_at": 1}  # Sort messages chronologically
                    }
                ],
                "as": "chat_messages"
            }
        },
        # Lookup CTA information
        {
            "$lookup": {
                "from": "cta",
                "let": {"cta_ids": "$response.call_to_action.id"},
                "pipeline": [
                    {
                        "$match": {
                            "$expr": {
                                "$in": ["$_id", {
                                    "$map": {
                                        "input": {"$ifNull": ["$$cta_ids", []]},
                                        "as": "id",
                                        "in": {"$toObjectId": "$$id"}
                                    }
                                }]
                            }
                        }
                    }
                ],
                "as": "cta_details"
            }
        }
    ]


async def process_conversation_async(conversation, minio_client):
    """Process a single conversation with all its messages asynchronously"""
    try:
        chat_messages = conversation.get("chat_messages", [])
        if not chat_messages:
            return []

        # Pre-process bot response data once
        bot_response = conversation
        global_reply_urls = []
        if bot_response:
            reply_urls_raw = bot_response.get("response", {}).get("reply_urls", [])
            global_reply_urls = [url for url in reply_urls_raw if url and isinstance(url, str)]

        # Pre-process CTA lookup for efficiency
        cta_lookup = {str(cta["_id"]): cta for cta in conversation.get("cta_details", [])}

        # Process all media URLs in parallel for all messages
        all_media_tasks = []
        message_media_map = {}

        for i, message in enumerate(chat_messages):
            media_ids = message.get("media_ids", [])
            if media_ids:
                task = process_media_urls_async(media_ids, minio_client)
                all_media_tasks.append(task)
                message_media_map[i] = len(all_media_tasks) - 1

        # Execute all media processing in parallel
        if all_media_tasks:
            media_results = await asyncio.gather(*all_media_tasks, return_exceptions=True)
        else:
            media_results = []

        # Process messages with pre-computed data
        processed_messages = []
        seen_reply_urls = False

        for i, message in enumerate(chat_messages):
            # Get pre-processed media URLs
            media_urls = []
            if i in message_media_map:
                media_result = media_results[message_media_map[i]]
                if not isinstance(media_result, Exception):
                    media_urls = media_result

            # Handle assistant messages
            if message.get("role") == "assistant" and bot_response:
                if not seen_reply_urls:
                    seen = {}
                    all_urls = media_urls + global_reply_urls
                    for url in all_urls:
                        if url and isinstance(url, str):
                            key = strip_query(url)
                            if key not in seen:
                                seen[key] = url
                    message["reply_urls"] = list(seen.values())
                    seen_reply_urls = True
                else:
                    message["reply_urls"] = [url for url in media_urls if url and isinstance(url, str)]

                # Add bot metadata efficiently
                add_bot_metadata_sync(message, bot_response, cta_lookup)

            elif message.get("role") == "user":
                # Add user metadata
                ai_enabled = bot_response.get("response", {}).get("ai_enabled", True)
                has_credit = message.get("has_credit")
                if has_credit is None:
                    has_credit = bot_response.get("response", {}).get("has_credit", True)

                message.update({
                    "ai_enabled": ai_enabled,
                    "has_credit": has_credit
                })

            # Convert ObjectId to string and create message dict efficiently
            message["_id"] = str(message["_id"])

            # Create Message object only with required fields to reduce memory
            try:
                processed_messages.append(Message(**message).dict())
            except Exception as e:
                loggers.warning(f"Error creating message object: {e}")
                # Fallback: create basic message dict
                processed_messages.append({
                    "_id": message["_id"],
                    "role": message.get("role", "user"),
                    "content": message.get("content", ""),
                    "created_at": message.get("created_at"),
                })

        return processed_messages

    except Exception as e:
        loggers.error(f"Error processing conversation: {e}")
        return []


async def process_media_urls_async(media_ids, minio_client):
    """Process media URLs asynchronously with better error handling"""
    if not media_ids:
        return []

    media_ids = flatten_deep(media_ids)
    media_urls = []

    for media_id in media_ids:
        try:
            media_id_str = str(media_id)
            if media_id_str.startswith(('http://', 'https://')):
                media_urls.append(media_id_str)
                continue

            new_url = minio_client.get_presigned_url(
                object_name=media_id_str,
                folder="Images/test" if not media_id_str.startswith("Images") else None
            )
            if new_url:
                media_urls.append(new_url)
        except Exception as e:
            loggers.warning(f"Error processing media ID {media_id}: {str(e)}")
            continue

    return media_urls


def add_bot_metadata_sync(message, bot_response, cta_lookup):
    """Add bot metadata to assistant messages synchronously for better performance"""
    # Process CTA information efficiently
    cta_list = []

    for cta in bot_response.get("response", {}).get("call_to_action", []):
        cta_id = cta.get("id")
        cta_status = cta.get("status")
        if cta_id and str(cta_id) in cta_lookup:
            cta_status = cta_lookup[str(cta_id)].get("status", cta_status)

        cta_list.append({
            "id": cta_id,
            "cta_status": cta_status,
            "issue_type": cta.get("type")
        })

    # Process information gathering efficiently
    info_gathering = bot_response.get("response", {}).get("information_gathering", [])
    for info in info_gathering:
        arga = info.get("function_args", {})
        if cta_id := arga.get("cta_id"):
            if str(cta_id) in cta_lookup:
                arga["cta_status"] = cta_lookup[str(cta_id)].get("status")

    # Update message with bot metadata - only include non-None values to reduce memory
    response_data = bot_response.get("response", {})

    updates = {
        "cta": cta_list,
        "ai_enabled": response_data.get("ai_enabled", True),
        "has_credit": response_data.get("has_credit", True)
    }

    # Only add fields that have values to reduce memory usage
    optional_fields = {
        "information_gathering": [InformationGathering(**i) for i in info_gathering] if info_gathering else None,
        "next_spin_state": response_data.get("next_spin_state"),
        "current_spin_state": response_data.get("current_spin_state"),
        "summary": response_data.get("chat_summary"),
        "products": "Beauty Aesthetics",
        "sentiment": response_data.get("sentiment"),
        "language": response_data.get("language"),
        "processing_time": response_data.get("processing_time"),
        "metadata": response_data.get("metadata"),
        "evaluation_id": response_data.get("evaluation_id"),
        "evaluation_status": response_data.get("evaluation_status"),
    }

    # Only add non-None values
    for key, value in optional_fields.items():
        if value is not None:
            updates[key] = value

    message.update(updates)



def flatten_deep(data):
    result = []
    for item in data:
        if isinstance(item, list):
            result.extend(flatten_deep(item))
        else:
            result.append(item)
    return result

@router.post("/delete_conversation")
async def delete_conversation(user_id: str, current_user: UserTenantDB = Depends(get_tenant_info)):
    try:
        #chatmessages
        current_user.db.chat_messages.delete_many({"user_id": user_id})
        #airesponse
        current_user.db.ai_response.delete_many({"request.user_id": user_id})

        #customers
        current_user.db.customers.update_one(
            {"customer_id": user_id},
            {"$unset": {"spin_states": "", "current_state": "", "next_state": ""}}
        )
        return JSONResponse(status_code=200, content={"message": "Chat Cleared"})
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/additional_information")
async def get_additional_information(response_id: str, current_user: UserTenantDB = Depends(get_tenant_info)):
    try:
        # Initialize database and configuration
        db = current_user.async_db
        env_var = current_user.db.settings.find_one({"name": "env"})
        if not env_var:
            raise HTTPException(status_code=404, detail="Environment configuration not found")

        # Initialize Qdrant client
        qd_config = env_var.get("qdrant_config", {})
        qd_config_ = QdrantConfig(
            coll_name=qd_config.get("page_collection"),
            host=qd_config.get("host"),
            port=qd_config.get("port")
        )
        qd_client = Qdrant_Call(config=qd_config_).client_()

        # Initialize MinIO client
        minio_config = env_var.get("minio_config", {})
        minio_client = MinIOClient(config=MinIOConfig(
            access_key=minio_config.get("access_key"),
            secret_key=minio_config.get("secret_key"),
            minio_url=minio_config.get("minio_url"),
            bucket_name=minio_config.get("bucket_name")
        ))

        # Retrieve AI response
        ai_response = await db.ai_response.find_one({"_id": ObjectId(response_id)})
        if not ai_response:
            raise HTTPException(status_code=404, detail="AI response not found")

        source_nodes = ai_response.get("response", {}).get("source_nodes", [])

        # Process source nodes in parallel
        processed_data = await process_source_nodes(source_nodes, db,qd_client, qd_config, minio_client)
        extra_detail=ai_response.get("response", {}).get("information_gathering", [])
        add_info=[]
        for i in extra_detail:
            arga=i.get("function_args")
            if arga.get("cta_id"):
                cta_data=await get_cta_item(arga.get("cta_id"),current_user.tenant_id,current_user.async_db)
                add_info.append(cta_data)



        # Prepare response
        response = {
            "additional_information":add_info,
            "metadata": processed_data["source_data"],
            "source_nodes": processed_data["final_data"]
        }

        return response

    except Exception as e:
        # Log the exception for debugging
        import traceback
        print(f"Error in get_additional_information: {str(e)}")
        print(traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(e))


async def process_source_nodes(source_nodes,db, qd_client, qd_config, minio):
    """Process source nodes in parallel for better performance."""
    # Group nodes by source and hash_id
    node_groups = {}
    for node in source_nodes:
        metadata = node.get("node", {}).get("metadata", {})
        hash_id = metadata.get("hash_id")
        sent_id = metadata.get("sent_id")
        source = metadata.get("source")

        if not hash_id:
            continue

        key = f"{source}:{hash_id}"
        if key not in node_groups:
            node_groups[key] = {
                "hash_id": hash_id,
                "source": source,
                "images": metadata.get("images", []),
                "metadata": metadata,
                "sent_ids": set(),
                "nodes": []
            }

        # Add sentence ID if not already present
        if sent_id and sent_id not in node_groups[key]["sent_ids"]:
            node_groups[key]["sent_ids"].add(sent_id)
            node_groups[key]["nodes"].append({
                "node": node,
                "sent_id": sent_id
            })

    # Process each unique source/hash_id group in parallel
    tasks = [process_node_group(group_data,db, qd_client, qd_config, minio)
             for group_key, group_data in node_groups.items()]

    # Execute all tasks concurrently
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # Collect results
    source_data = []
    final_data = []

    for result in results:
        if isinstance(result, Exception):
            print(f"Error processing node group: {str(result)}")
            continue

        if result:
            if result["source_data"]:
                source_data.append(result["source_data"])
            if result["page_node"]:
                final_data.append(result["page_node"])

    return {
        "source_data": source_data,
        "final_data": final_data
    }


async def process_node_group(group_data,db, qd_client, qd_config, minio):
    """Process a group of nodes with the same source and hash_id."""
    try:
        hash_id = group_data["hash_id"]
        source = group_data["source"]
        images = group_data["images"]
        metadata = group_data["metadata"]

        # Start the page data query early to maximize parallelism
        page_data_task = asyncio.create_task(query_page_data(hash_id, qd_client, qd_config))

        # Generate presigned URLs (not async)
        source_url = minio.get_presigned_url(object_name=source, folder="Files")
        images_url = [
            minio.get_presigned_url(object_name=image, folder=f"Images/{source}")
            for image in images
        ]

        # Process each sentence ID in parallel - start these tasks early
        sentence_tasks = [
            asyncio.create_task(get_sentence_data(node_item["sent_id"], qd_client, qd_config))
            for node_item in group_data["nodes"]
        ]

        # Wait for async tasks to complete
        page_data = await page_data_task
        sentence_results = await asyncio.gather(*sentence_tasks, return_exceptions=True)

        # Create source data
        source_data = {
            "source": source,
            "source_url": source_url,
            "images": images,
            "images_url": images_url,
        }

        if not page_data or not page_data.points:
            return {"source_data": source_data, "page_node": None}

        # Extract valid sentence texts
        sentences = []
        for result in sentence_results:
            if isinstance(result, Exception):
                print(f"Error processing sentence: {str(result)}")
                continue

            if result and result.get("text"):
                sentences.append(result["text"])

        # Extract content and metadata from page data
        page_metadata = page_data.points[0].payload
        page_node_content = json.loads(page_metadata.get("_node_content", "{}"))
        page_metadata.pop("_node_content", None)

        updated_by_id=page_metadata.get("updated_by")

        updated_by_task=db.users.find_one({"_id":ObjectId(updated_by_id)},{"username":1})
        updated_by =await updated_by_task

        # Add metadata to source_data from page_metadata
        source_data["created_at"] = page_metadata.get("created_at")
        source_data["updated_at"] = page_metadata.get("updated_at")
        source_data["updated_by"] =updated_by.get("username")

        # Create page node with sentences as a list and include metadata from page node
        page_node = {
            "metadata": page_metadata,
            "text": page_node_content.get("text_resource", {}).get("text", ""),
            "sentences": sentences,  # Now a list of sentence texts
            "created_at": page_metadata.get("created_at"),
            "updated_at": page_metadata.get("updated_at"),
            "updated_by": updated_by.get("username")
        }

        return {"source_data": source_data, "page_node": page_node}

    except Exception as e:
        print(f"Error in process_node_group: {str(e)}")
        return e


async def query_page_data(hash_id, qd_client, qd_config):
    """Query page data as a separate async function for better parallelism."""
    return qd_client.query_points(
        collection_name=qd_config.get("page_collection"),
        query_filter=models.Filter(
            must=[models.FieldCondition(key="hash_id", match=models.MatchValue(value=hash_id))]
        )
    )


async def get_sentence_data(sent_id, qd_client, qd_config):
    """Fetch sentence data for a specific sentence ID."""
    try:
        # Query sentence data
        sentence_data = qd_client.query_points(
            collection_name=qd_config.get("sentence_split_collection"),
            query_filter=models.Filter(
                must=[models.FieldCondition(key="sent_id", match=models.MatchValue(value=sent_id))]
            )
        )

        if not sentence_data or not sentence_data.points:
            return None

        # Extract sentence content
        sentence_node_content = json.loads(sentence_data.points[0].payload.get("_node_content", "{}"))
        sentence_text = sentence_node_content.get("text_resource", {}).get("text", "")

        return {"text": sentence_text}

    except Exception as e:
        print(f"Error in get_sentence_data: {str(e)}")
        return e


@router.get("/get_products", response_model=List[str])
async def get_products(userTenant: UserTenantDB = Depends(get_tenant_info)):
    products = userTenant.db.ai_response.distinct("response.identified_product")
    # Filter out None or empty string products if needed
    return await clean_list(products)


@router.get("/get_languages", response_model=List[str])
async def get_languages(userTenant: UserTenantDB = Depends(get_tenant_info)):
    # languages = userTenant.db.ai_response.distinct("response.language")
    languages = userTenant.db.ai_response.distinct(
    "response.language",
    {
        "response.language": {
            "$nin": [None,"Null","", [], {}]
        }
    }
)




    # languages_ = [i for i in languages if len(i.strip()) < 15]
    languages_=[i for i in languages if i]
    languages_ = [i for i in languages if isinstance(i, str) and len(i.strip()) < 15]
    unique_languages = set()
    for lang in languages_:
        if lang and isinstance(lang, str):
            normalized_lang = lang.strip().capitalize()
            if normalized_lang:
                unique_languages.add(normalized_lang)

    # Convert to sorted list
    return sorted(list(unique_languages))


#    return await clean_list(languages_)



@router.get("/get_sentiment", response_model=List[str])
async def get_sentiment(userTenant: UserTenantDB = Depends(get_tenant_info)):
    sentiment = userTenant.db.ai_response.distinct("response.sentiment")
    return await clean_list(sentiment        )


async def clean_list(data: List[Optional[str]]) -> List[str]:
    return [item.strip() for item in data if item and item.strip()]
